import sharp from "sharp"

export interface ImageDimensions {
  width: number
  height: number
}

export interface ImageInfo {
  buffer: Buffer
  dimensions: ImageDimensions
  orientation: "landscape" | "portrait"
}

export interface CompositeLayout {
  type: "vertical_stack" | "mixed_layout" | "side_by_side_with_text"
  textArea: { width: number; height: number; x: number; y: number }
  imageA: { width: number; height: number; x: number; y: number }
  imageB: { width: number; height: number; x: number; y: number }
  canvasSize: ImageDimensions
}

/**
 * Determines if an image is landscape or portrait based on dimensions
 */
export function getImageOrientation(dimensions: ImageDimensions): "landscape" | "portrait" {
  return dimensions.width > dimensions.height ? "landscape" : "portrait"
}

/**
 * Loads image from buffer and extracts metadata
 */
export async function loadImageInfo(imageBuffer: Buffer): Promise<ImageInfo> {
  if (!Buffer.isBuffer(imageBuffer) || imageBuffer.length === 0) {
    throw new Error("Invalid image buffer provided")
  }

  try {
    const image = sharp(imageBuffer)
    const metadata = await image.metadata()

    if (!metadata.width || !metadata.height) {
      throw new Error("Unable to determine image dimensions")
    }

    // Validate reasonable image dimensions
    if (metadata.width > 10000 || metadata.height > 10000) {
      throw new Error("Image dimensions too large (max 10000x10000)")
    }

    if (metadata.width < 1 || metadata.height < 1) {
      throw new Error("Image dimensions too small")
    }

    const dimensions: ImageDimensions = {
      width: metadata.width,
      height: metadata.height,
    }

    return {
      buffer: imageBuffer,
      dimensions,
      orientation: getImageOrientation(dimensions),
    }
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to load image: ${error.message}`)
    }
    throw new Error("Failed to load image: Unknown error")
  }
}

/**
 * Calculates proportional scaling to fit target dimensions while maintaining aspect ratio
 */
export function calculateScaling(
  original: ImageDimensions,
  target: ImageDimensions,
): { width: number; height: number; scale: number } {
  const scaleX = target.width / original.width
  const scaleY = target.height / original.height
  const scale = Math.min(scaleX, scaleY)

  return {
    width: Math.round(original.width * scale),
    height: Math.round(original.height * scale),
    scale,
  }
}

/**
 * Determines which image should remain unscaled (higher resolution)
 */
export function determineHigherResolution(imageA: ImageInfo, imageB: ImageInfo): "A" | "B" {
  const resolutionA = imageA.dimensions.width * imageA.dimensions.height
  const resolutionB = imageB.dimensions.width * imageB.dimensions.height
  return resolutionA >= resolutionB ? "A" : "B"
}

/**
 * Calculates dynamic font size based on canvas dimensions
 */
export function calculateFontSize(canvasWidth: number, canvasHeight: number): number {
  // Base font size calculation: use a percentage of the smaller dimension
  // This ensures text remains readable regardless of canvas size
  const baseFontSize = Math.min(canvasWidth, canvasHeight) * 0.03

  // Clamp between reasonable bounds
  const minFontSize = 16
  const maxFontSize = 72

  return Math.max(minFontSize, Math.min(maxFontSize, Math.round(baseFontSize)))
}

/**
 * Estimates text dimensions for layout calculations
 */
export function estimateTextDimensions(
  text: string,
  fontSize: number,
  maxWidth: number,
): { width: number; height: number } {
  // Rough estimation: average character width is about 0.6 * fontSize
  const avgCharWidth = fontSize * 0.6
  const charsPerLine = Math.floor(maxWidth / avgCharWidth)
  const lines = Math.ceil(text.length / charsPerLine)

  // Line height is typically 1.2 * fontSize
  const lineHeight = fontSize * 1.2
  const padding = fontSize * 0.5 // Some padding around text

  return {
    width: Math.min(text.length * avgCharWidth, maxWidth),
    height: lines * lineHeight + padding * 2,
  }
}

/**
 * Calculates layout for both images landscape (vertical stacking)
 */
function calculateVerticalStackLayout(
  imageA: ImageInfo,
  imageB: ImageInfo,
  text: string,
  higherRes: "A" | "B",
): CompositeLayout {
  const baseImage = higherRes === "A" ? imageA : imageB
  const otherImage = higherRes === "A" ? imageB : imageA

  // Canvas width is based on the higher resolution image
  const canvasWidth = baseImage.dimensions.width

  // Calculate font size and text dimensions
  const fontSize = calculateFontSize(canvasWidth, baseImage.dimensions.height)
  const textDims = estimateTextDimensions(text, fontSize, canvasWidth)

  // Scale the other image to match canvas width
  const otherScaled = calculateScaling(otherImage.dimensions, {
    width: canvasWidth,
    height: otherImage.dimensions.height,
  })

  const canvasHeight = textDims.height + baseImage.dimensions.height + otherScaled.height

  return {
    type: "vertical_stack",
    textArea: { width: canvasWidth, height: textDims.height, x: 0, y: 0 },
    imageA:
      higherRes === "A"
        ? { width: baseImage.dimensions.width, height: baseImage.dimensions.height, x: 0, y: textDims.height }
        : {
            width: otherScaled.width,
            height: otherScaled.height,
            x: 0,
            y: textDims.height + baseImage.dimensions.height,
          },
    imageB:
      higherRes === "B"
        ? { width: baseImage.dimensions.width, height: baseImage.dimensions.height, x: 0, y: textDims.height }
        : {
            width: otherScaled.width,
            height: otherScaled.height,
            x: 0,
            y: textDims.height + baseImage.dimensions.height,
          },
    canvasSize: { width: canvasWidth, height: canvasHeight },
  }
}

/**
 * Calculates layout for both images portrait (side-by-side with text on top)
 */
function calculateSideBySideLayout(
  imageA: ImageInfo,
  imageB: ImageInfo,
  text: string,
  higherRes: "A" | "B",
): CompositeLayout {
  const baseImage = higherRes === "A" ? imageA : imageB
  const otherImage = higherRes === "A" ? imageB : imageA

  // Canvas width is base image width + scaled other image width
  const otherScaled = calculateScaling(otherImage.dimensions, {
    width: otherImage.dimensions.width,
    height: baseImage.dimensions.height,
  })

  const canvasWidth = baseImage.dimensions.width + otherScaled.width
  const imageHeight = baseImage.dimensions.height

  // Calculate text dimensions
  const fontSize = calculateFontSize(canvasWidth, imageHeight)
  const textDims = estimateTextDimensions(text, fontSize, canvasWidth)

  const canvasHeight = textDims.height + imageHeight

  return {
    type: "side_by_side_with_text",
    textArea: { width: canvasWidth, height: textDims.height, x: 0, y: 0 },
    imageA:
      higherRes === "A"
        ? { width: baseImage.dimensions.width, height: baseImage.dimensions.height, x: 0, y: textDims.height }
        : {
            width: otherScaled.width,
            height: otherScaled.height,
            x: baseImage.dimensions.width,
            y: textDims.height,
          },
    imageB:
      higherRes === "B"
        ? { width: baseImage.dimensions.width, height: baseImage.dimensions.height, x: 0, y: textDims.height }
        : {
            width: otherScaled.width,
            height: otherScaled.height,
            x: baseImage.dimensions.width,
            y: textDims.height,
          },
    canvasSize: { width: canvasWidth, height: canvasHeight },
  }
}

/**
 * Calculates layout for mixed orientations (one portrait, one landscape)
 */
function calculateMixedLayout(
  imageA: ImageInfo,
  imageB: ImageInfo,
  text: string,
  higherRes: "A" | "B",
): CompositeLayout {
  const baseImage = higherRes === "A" ? imageA : imageB

  // Determine which is portrait and which is landscape
  const portraitImage = imageA.orientation === "portrait" ? imageA : imageB
  const landscapeImage = imageA.orientation === "landscape" ? imageA : imageB
  const isPortraitHigherRes = portraitImage === baseImage

  // Canvas width is based on the landscape image (spans full width at bottom)
  const canvasWidth = Math.max(landscapeImage.dimensions.width, baseImage.dimensions.width)

  // Calculate font size
  const fontSize = calculateFontSize(
    canvasWidth,
    Math.max(portraitImage.dimensions.height, landscapeImage.dimensions.height),
  )

  // Upper section: text and portrait image side by side
  const portraitScaled = isPortraitHigherRes
    ? portraitImage.dimensions
    : calculateScaling(portraitImage.dimensions, {
        width: Math.floor(canvasWidth * 0.4), // Portrait takes up ~40% of width
        height: portraitImage.dimensions.height,
      })

  const textAreaWidth = canvasWidth - portraitScaled.width
  const textDims = estimateTextDimensions(text, fontSize, textAreaWidth)
  const upperSectionHeight = Math.max(textDims.height, portraitScaled.height)

  // Lower section: landscape image spans full width
  const landscapeScaled = calculateScaling(landscapeImage.dimensions, {
    width: canvasWidth,
    height: landscapeImage.dimensions.height,
  })

  const canvasHeight = upperSectionHeight + landscapeScaled.height

  // Position elements
  const textX = portraitImage === imageA ? portraitScaled.width : 0
  const portraitX = portraitImage === imageA ? 0 : textAreaWidth

  return {
    type: "mixed_layout",
    textArea: {
      width: textAreaWidth,
      height: Math.max(textDims.height, upperSectionHeight),
      x: textX,
      y: 0,
    },
    imageA:
      imageA.orientation === "portrait"
        ? { width: portraitScaled.width, height: portraitScaled.height, x: portraitX, y: 0 }
        : { width: landscapeScaled.width, height: landscapeScaled.height, x: 0, y: upperSectionHeight },
    imageB:
      imageB.orientation === "portrait"
        ? { width: portraitScaled.width, height: portraitScaled.height, x: portraitX, y: 0 }
        : { width: landscapeScaled.width, height: landscapeScaled.height, x: 0, y: upperSectionHeight },
    canvasSize: { width: canvasWidth, height: canvasHeight },
  }
}

/**
 * Main function to calculate the appropriate layout based on image orientations
 */
export function calculateLayout(imageA: ImageInfo, imageB: ImageInfo, text: string): CompositeLayout {
  const higherRes = determineHigherResolution(imageA, imageB)

  if (imageA.orientation === "landscape" && imageB.orientation === "landscape") {
    // Both landscape: vertical stacking
    return calculateVerticalStackLayout(imageA, imageB, text, higherRes)
  } else if (imageA.orientation === "portrait" && imageB.orientation === "portrait") {
    // Both portrait: side-by-side with text on top
    return calculateSideBySideLayout(imageA, imageB, text, higherRes)
  } else {
    // Mixed orientations: text and portrait side-by-side on top, landscape on bottom
    return calculateMixedLayout(imageA, imageB, text, higherRes)
  }
}

/**
 * Creates a text image with white text on black background
 */
export async function createTextImage(
  text: string,
  width: number,
  height: number,
  fontSize: number,
): Promise<Buffer> {
  if (!text || text.trim().length === 0) {
    throw new Error("Text content is required")
  }

  if (width <= 0 || height <= 0) {
    throw new Error("Invalid dimensions for text image")
  }

  if (fontSize <= 0 || fontSize > 200) {
    throw new Error("Invalid font size (must be between 1 and 200)")
  }

  try {
    // Create SVG with text
    const lineHeight = fontSize * 1.2
    const padding = fontSize * 0.5

    // Split text into lines that fit the width
    const avgCharWidth = fontSize * 0.6
    const charsPerLine = Math.floor((width - padding * 2) / avgCharWidth)
    const words = text.split(" ")
    const lines: string[] = []
    let currentLine = ""

    for (const word of words) {
      if ((currentLine + " " + word).length <= charsPerLine) {
        currentLine = currentLine ? currentLine + " " + word : word
      } else {
        if (currentLine) lines.push(currentLine)
        currentLine = word
      }
    }
    if (currentLine) lines.push(currentLine)

    // Escape HTML entities in text to prevent SVG injection
    const escapeHtml = (str: string) =>
      str
        .replace(/&/g, "&amp;")
        .replace(/</g, "&lt;")
        .replace(/>/g, "&gt;")
        .replace(/"/g, "&quot;")
        .replace(/'/g, "&#39;")

    // Create SVG text elements
    const textElements = lines
      .map((line, index) => {
        const y = padding + (index + 1) * lineHeight
        const escapedLine = escapeHtml(line)
        return `<text x="${padding}" y="${y}" font-family="Arial, sans-serif" font-size="${fontSize}" fill="white">${escapedLine}</text>`
      })
      .join("\n")

    const svg = `
      <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
        <rect width="100%" height="100%" fill="black"/>
        ${textElements}
      </svg>
    `

    return sharp(Buffer.from(svg)).png().toBuffer()
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to create text image: ${error.message}`)
    }
    throw new Error("Failed to create text image: Unknown error")
  }
}

/**
 * Creates the final composite image
 */
export async function createCompositeImage(
  imageA: ImageInfo,
  imageB: ImageInfo,
  text: string,
): Promise<Buffer> {
  if (!imageA || !imageB) {
    throw new Error("Both images are required")
  }

  if (!text || text.trim().length === 0) {
    throw new Error("Text content is required")
  }

  if (text.length > 1000) {
    throw new Error("Text content too long (max 1000 characters)")
  }

  try {
    const layout = calculateLayout(imageA, imageB, text)

    // Validate layout dimensions
    if (layout.canvasSize.width > 15000 || layout.canvasSize.height > 15000) {
      throw new Error("Composite image would be too large")
    }

    // Create text image
    const fontSize = calculateFontSize(layout.canvasSize.width, layout.canvasSize.height)
    const textImage = await createTextImage(text, layout.textArea.width, layout.textArea.height, fontSize)

    // Prepare images for compositing
    const resizedImageA = await sharp(imageA.buffer)
      .resize(layout.imageA.width, layout.imageA.height, { fit: "fill" })
      .toBuffer()

    const resizedImageB = await sharp(imageB.buffer)
      .resize(layout.imageB.width, layout.imageB.height, { fit: "fill" })
      .toBuffer()

    // Create composite
    const composite = sharp({
      create: {
        width: layout.canvasSize.width,
        height: layout.canvasSize.height,
        channels: 3,
        background: { r: 255, g: 255, b: 255 },
      },
    })

    const compositeOperations = [
      { input: textImage, left: layout.textArea.x, top: layout.textArea.y },
      { input: resizedImageA, left: layout.imageA.x, top: layout.imageA.y },
      { input: resizedImageB, left: layout.imageB.x, top: layout.imageB.y },
    ]

    return composite.composite(compositeOperations).jpeg({ quality: 90 }).toBuffer()
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to create composite image: ${error.message}`)
    }
    throw new Error("Failed to create composite image: Unknown error")
  }
}
