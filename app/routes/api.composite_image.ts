import type { ActionFunctionArgs } from "@remix-run/node"
import fs from "fs/promises"
import path from "path"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"
import { loadImageInfo, createCompositeImage } from "~/utils/image_composition"

export const action = Handle(
  async ({ request }: ActionFunctionArgs) => {
    if (request.method !== Method.POST) {
      return new Response("not allowed", { status: Status.MethodNotAllowed })
    }

    try {
      const formData = await request.formData()

      // Extract and validate required parameters
      const textContent = formData.get("text") as string | null
      const imageAPath = formData.get("image_a_path") as string | null
      const imageBPath = formData.get("image_b_path") as string | null

      // Validate required parameters
      if (!textContent) {
        throw new Response("text content missing", { status: Status.BadRequest })
      }
      if (!imageAPath) {
        throw new Response("image_a_path missing", { status: Status.BadRequest })
      }
      if (!imageBPath) {
        throw new Response("image_b_path missing", { status: Status.BadRequest })
      }

      // Validate text content is not empty
      if (textContent.trim().length === 0) {
        throw new Response("text content cannot be empty", { status: Status.BadRequest })
      }

      // Load and validate image files
      let imageABuffer: Buffer
      let imageBBuffer: Buffer

      try {
        // Resolve paths relative to the workspace root
        const resolvedImageAPath = path.resolve(imageAPath)
        const resolvedImageBPath = path.resolve(imageBPath)

        // Security check: ensure paths are within allowed directories
        // This prevents directory traversal attacks
        const workspaceRoot = process.cwd()
        if (!resolvedImageAPath.startsWith(workspaceRoot) || !resolvedImageBPath.startsWith(workspaceRoot)) {
          throw new Response("invalid file path", { status: Status.BadRequest })
        }

        // Read image files
        imageABuffer = await fs.readFile(resolvedImageAPath)
        imageBBuffer = await fs.readFile(resolvedImageBPath)
      } catch (error) {
        console.error("Error reading image files:", error)
        if (error instanceof Error && error.message.includes("ENOENT")) {
          throw new Response("image file not found", { status: Status.NotFound })
        }
        throw new Response("error reading image files", { status: Status.InternalServerError })
      }

      // Validate that files are actually images by loading metadata
      let imageAInfo, imageBInfo
      try {
        imageAInfo = await loadImageInfo(imageABuffer)
        imageBInfo = await loadImageInfo(imageBBuffer)
      } catch (error) {
        console.error("Error loading image metadata:", error)
        throw new Response("invalid image format", { status: Status.BadRequest })
      }

      // Create composite image
      let compositeBuffer: Buffer
      try {
        compositeBuffer = await createCompositeImage(imageAInfo, imageBInfo, textContent)
      } catch (error) {
        console.error("Error creating composite image:", error)
        throw new Response("error creating composite image", { status: Status.InternalServerError })
      }

      // Return the composite image
      return new Response(compositeBuffer, {
        status: Status.Ok,
        headers: {
          "Content-Type": "image/jpeg",
          "Content-Length": compositeBuffer.length.toString(),
          "Cache-Control": "public, max-age=3600", // Cache for 1 hour
        },
      })
    } catch (error) {
      // If error is already a Response, re-throw it
      if (error instanceof Response) {
        throw error
      }

      // Log unexpected errors
      console.error("Unexpected error in composite image API:", error)
      throw new Response("internal server error", { status: Status.InternalServerError })
    }
  },
  { auth: false },
)
