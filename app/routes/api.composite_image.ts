import type { ActionFunctionArgs } from "@remix-run/node"

import { HttpStatusCode as Status, HttpMethod as Method } from "~/utils/http_code"
import Handle from "~/utils/handle_request"
import { loadImageInfo, createCompositeImage } from "~/utils/image_composition"

export const action = Handle(
  async ({ request }: ActionFunctionArgs) => {
    if (request.method !== Method.POST) {
      return new Response("not allowed", { status: Status.MethodNotAllowed })
    }

    try {
      const formData = await request.formData()

      const textContent = formData.get("text") as string | null
      const imageAUrl = formData.get("image_a_url") as string | null
      const imageBUrl = formData.get("image_b_url") as string | null

      // Validate required parameters
      if (!textContent) {
        throw new Response("text content missing", { status: Status.BadRequest })
      }
      if (!imageAUrl) {
        throw new Response("image_a_url missing", { status: Status.BadRequest })
      }
      if (!imageBUrl) {
        throw new Response("image_b_url missing", { status: Status.BadRequest })
      }

      // Validate URLs
      try {
        const urlA = new URL(imageAUrl)
        const urlB = new URL(imageBUrl)
      } catch (error) {
        if (error instanceof Response) throw error
        throw new Response("invalid URL format", { status: Status.BadRequest })
      }

      let imageABuffer: Buffer
      let imageBBuffer: Buffer

      try {
        const [responseA, responseB] = await Promise.all([fetch(imageAUrl), fetch(imageBUrl)])

        if (!responseA.ok) {
          throw new Response(`failed to fetch image A: ${responseA.status} ${responseA.statusText}`, {
            status: Status.BadRequest,
          })
        }
        if (!responseB.ok) {
          throw new Response(`failed to fetch image B: ${responseB.status} ${responseB.statusText}`, {
            status: Status.BadRequest,
          })
        }

        // Convert responses to buffers
        const [arrayBufferA, arrayBufferB] = await Promise.all([
          responseA.arrayBuffer(),
          responseB.arrayBuffer(),
        ])

        imageABuffer = Buffer.from(arrayBufferA)
        imageBBuffer = Buffer.from(arrayBufferB)
      } catch (error) {
        if (error instanceof Response) throw error
        console.error("Error fetching images:", error)
        throw new Response("error fetching images from URLs", { status: Status.InternalServerError })
      }

      // Validate that files are actually images by loading metadata
      let imageAInfo, imageBInfo
      try {
        imageAInfo = await loadImageInfo(imageABuffer)
        imageBInfo = await loadImageInfo(imageBBuffer)
      } catch (error) {
        console.error("Error loading image metadata:", error)
        throw new Response("invalid image format", { status: Status.BadRequest })
      }

      // Create composite image
      let compositeBuffer: Buffer
      try {
        compositeBuffer = await createCompositeImage(imageAInfo, imageBInfo, textContent)
      } catch (error) {
        console.error("Error creating composite image:", error)
        throw new Response("error creating composite image", { status: Status.InternalServerError })
      }

      // Return the composite image
      return new Response(compositeBuffer, {
        status: Status.Ok,
        headers: {
          "Content-Type": "image/jpeg",
          "Content-Length": compositeBuffer.length.toString(),
          "Cache-Control": "public, max-age=3600", // Cache for 1 hour
        },
      })
    } catch (error) {
      // If error is already a Response, re-throw it
      if (error instanceof Response) {
        throw error
      }

      // Log unexpected errors
      console.error("Unexpected error in composite image API:", error)
      throw new Response("internal server error", { status: Status.InternalServerError })
    }
  },
  { auth: false },
)
